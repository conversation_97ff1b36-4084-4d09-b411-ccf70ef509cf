<?php
// Relatório Analítico de Acordos

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;
use Dompdf\Options;

// Construir WHERE clause baseado nos filtros
$where_conditions = [];
$params = [];

if (!empty($filtros['data_inicio'])) {
    $where_conditions[] = "a.data_acordo >= ?";
    $params[] = $filtros['data_inicio'];
}

if (!empty($filtros['data_fim'])) {
    $where_conditions[] = "a.data_acordo <= ?";
    $params[] = $filtros['data_fim'];
}

if (!empty($filtros['pa_id'])) {
    $where_conditions[] = "p.pa_id = ?";
    $params[] = $filtros['pa_id'];
}

if (!empty($filtros['advogado_id'])) {
    $where_conditions[] = "p.advogado_id = ?";
    $params[] = $filtros['advogado_id'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Query principal
$query = "
    SELECT 
        a.id,
        a.numero_repactuacao,
        p.numero_processo,
        p.associado_nome,
        p.associado_documento,
        pa.nome as pa_nome,
        adv.nome as advogado_nome,
        a.valor_acordo,
        a.quantidade_parcelas,
        a.porcentagem_honorario,
        a.ativo,
        DATE_FORMAT(a.data_acordo, '%d/%m/%Y') as data_acordo_formatada,
        DATE_FORMAT(a.data_vencimento_inicial, '%d/%m/%Y') as data_vencimento_inicial_formatada,
        (SELECT COUNT(*) FROM cbp_parcelas_acordo pa WHERE pa.acordo_id = a.id AND pa.status = 'PAGO') as parcelas_pagas,
        (SELECT COUNT(*) FROM cbp_parcelas_acordo pa WHERE pa.acordo_id = a.id AND pa.status = 'PENDENTE') as parcelas_pendentes,
        (SELECT COUNT(*) FROM cbp_parcelas_acordo pa WHERE pa.acordo_id = a.id AND pa.status = 'VENCIDO') as parcelas_vencidas,
        (SELECT COALESCE(SUM(pa.valor_parcela), 0) FROM cbp_parcelas_acordo pa WHERE pa.acordo_id = a.id AND pa.status = 'PAGO') as valor_pago,
        (SELECT COALESCE(SUM(pa.valor_parcela), 0) FROM cbp_parcelas_acordo pa WHERE pa.acordo_id = a.id AND pa.status != 'PAGO') as valor_pendente
    FROM cbp_acordos a
    LEFT JOIN cbp_processos_judiciais p ON a.processo_id = p.id
    LEFT JOIN pontos_atendimento pa ON p.pa_id = pa.id
    LEFT JOIN cbp_advogados adv ON p.advogado_id = adv.id
    $where_clause
    ORDER BY a.data_acordo DESC, a.id DESC
    LIMIT 1000
";

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$dados = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Função para formatar valor
function formatarValor($valor) {
    return $valor ? 'R$ ' . number_format($valor, 2, ',', '.') : '-';
}

// Função para formatar documento
function formatarDocumento($doc) {
    if (!$doc) return '-';
    $doc = preg_replace('/[^0-9]/', '', $doc);
    if (strlen($doc) === 11) {
        return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $doc);
    } else if (strlen($doc) === 14) {
        return preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $doc);
    }
    return $doc;
}

// Função para obter cor do status
function getStatusColor($ativo) {
    return $ativo ? 'success' : 'secondary';
}

// Função para obter texto do status
function getStatusTexto($ativo) {
    return $ativo ? 'Ativo' : 'Inativo';
}

// Gerar saída baseado no formato
if ($formato === 'html') {
    ?>
    <div class="relatorio-content">
        <div class="mb-3">
            <strong>Total de registros:</strong> <?= count($dados) ?>
            <?php if (count($dados) >= 1000): ?>
                <span class="text-warning">(Limitado a 1000 registros)</span>
            <?php endif; ?>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>Nº Repactuação</th>
                        <th>Nº Processo</th>
                        <th>Associado</th>
                        <th>PA</th>
                        <th>Status</th>
                        <th class="text-end">Valor Acordo</th>
                        <th class="text-center">Parcelas</th>
                        <th class="text-center">Pagas</th>
                        <th class="text-center">Pendentes</th>
                        <th class="text-end">Valor Pago</th>
                        <th>Data Acordo</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($dados as $row): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['numero_repactuacao'] ?: '-') ?></td>
                        <td><?= htmlspecialchars($row['numero_processo'] ?: '-') ?></td>
                        <td><?= htmlspecialchars($row['associado_nome'] ?: '-') ?></td>
                        <td><?= htmlspecialchars($row['pa_nome'] ?: '-') ?></td>
                        <td>
                            <span class="badge bg-<?= getStatusColor($row['ativo']) ?>">
                                <?= getStatusTexto($row['ativo']) ?>
                            </span>
                        </td>
                        <td class="text-end"><?= formatarValor($row['valor_acordo']) ?></td>
                        <td class="text-center"><?= $row['quantidade_parcelas'] ?: '-' ?></td>
                        <td class="text-center">
                            <span class="badge bg-success"><?= $row['parcelas_pagas'] ?></span>
                        </td>
                        <td class="text-center">
                            <span class="badge bg-warning"><?= $row['parcelas_pendentes'] ?></span>
                            <?php if ($row['parcelas_vencidas'] > 0): ?>
                                <span class="badge bg-danger"><?= $row['parcelas_vencidas'] ?></span>
                            <?php endif; ?>
                        </td>
                        <td class="text-end"><?= formatarValor($row['valor_pago']) ?></td>
                        <td><?= $row['data_acordo_formatada'] ?: '-' ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <?php if (empty($dados)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Nenhum acordo encontrado com os filtros aplicados.
        </div>
        <?php endif; ?>
    </div>
    <?php
} elseif ($formato === 'excel') {
    // Limpar qualquer output buffer antes de gerar o Excel
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Acordos Analítico');
    
    // Cabeçalhos
    $headers = [
        'A' => 'Nº Repactuação',
        'B' => 'Nº Processo',
        'C' => 'Associado',
        'D' => 'Documento',
        'E' => 'PA',
        'F' => 'Advogado',
        'G' => 'Status',
        'H' => 'Valor Acordo',
        'I' => 'Qtd Parcelas',
        'J' => 'Parcelas Pagas',
        'K' => 'Parcelas Pendentes',
        'L' => 'Parcelas Vencidas',
        'M' => 'Valor Pago',
        'N' => 'Valor Pendente',
        'O' => 'Data Acordo',
        'P' => 'Data Vencimento Inicial',
        'Q' => '% Honorários'
    ];
    
    // Definir cabeçalhos
    foreach ($headers as $col => $header) {
        $sheet->setCellValue($col . '1', $header);
    }
    $sheet->getStyle('A1:Q1')->getFont()->setBold(true);
    
    // Preencher dados
    $row = 2;
    foreach ($dados as $data) {
        $sheet->setCellValue('A' . $row, $data['numero_repactuacao'] ?: '');
        $sheet->setCellValue('B' . $row, $data['numero_processo'] ?: '');
        $sheet->setCellValue('C' . $row, $data['associado_nome'] ?: '');
        $sheet->setCellValue('D' . $row, $data['associado_documento'] ?: '');
        $sheet->setCellValue('E' . $row, $data['pa_nome'] ?: '');
        $sheet->setCellValue('F' . $row, $data['advogado_nome'] ?: '');
        $sheet->setCellValue('G' . $row, $data['ativo'] ? 'Ativo' : 'Inativo');
        $sheet->setCellValue('H' . $row, $data['valor_acordo'] ?: 0);
        $sheet->setCellValue('I' . $row, $data['quantidade_parcelas'] ?: 0);
        $sheet->setCellValue('J' . $row, $data['parcelas_pagas'] ?: 0);
        $sheet->setCellValue('K' . $row, $data['parcelas_pendentes'] ?: 0);
        $sheet->setCellValue('L' . $row, $data['parcelas_vencidas'] ?: 0);
        $sheet->setCellValue('M' . $row, $data['valor_pago'] ?: 0);
        $sheet->setCellValue('N' . $row, $data['valor_pendente'] ?: 0);
        $sheet->setCellValue('O' . $row, $data['data_acordo_formatada'] ?: '');
        $sheet->setCellValue('P' . $row, $data['data_vencimento_inicial_formatada'] ?: '');
        $sheet->setCellValue('Q' . $row, $data['porcentagem_honorario'] ?: 0);
        $row++;
    }
    
    // Auto-ajustar colunas
    foreach (range('A', 'Q') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    
    // Configurar download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="acordos_analitico_' . date('Y-m-d_H-i-s') . '.xlsx"');
    header('Cache-Control: max-age=0');
    
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
    
} elseif ($formato === 'pdf') {
    // Limpar qualquer output buffer antes de gerar o PDF
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);
    
    $dompdf = new Dompdf($options);
    
    $html = '
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; font-size: 9px; }
            .header { text-align: center; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; }
            th, td { border: 1px solid #ddd; padding: 3px; text-align: left; font-size: 8px; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .text-right { text-align: right; }
            .text-center { text-align: center; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>RELATÓRIO ANALÍTICO DE ACORDOS</h1>
            <p>Gerado em: ' . date('d/m/Y H:i:s') . '</p>
            <p>Total de registros: ' . count($dados) . '</p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Nº Repact.</th>
                    <th>Associado</th>
                    <th>PA</th>
                    <th>Status</th>
                    <th class="text-right">Valor</th>
                    <th class="text-center">Parc.</th>
                    <th>Data</th>
                </tr>
            </thead>
            <tbody>';
    
    foreach ($dados as $row) {
        $html .= '<tr>
            <td>' . htmlspecialchars($row['numero_repactuacao'] ?: '-') . '</td>
            <td>' . htmlspecialchars(substr($row['associado_nome'] ?: '-', 0, 20)) . '</td>
            <td>' . htmlspecialchars($row['pa_nome'] ?: '-') . '</td>
            <td>' . ($row['ativo'] ? 'Ativo' : 'Inativo') . '</td>
            <td class="text-right">' . formatarValor($row['valor_acordo']) . '</td>
            <td class="text-center">' . ($row['quantidade_parcelas'] ?: '-') . '</td>
            <td>' . ($row['data_acordo_formatada'] ?: '-') . '</td>
        </tr>';
    }
    
    $html .= '</tbody></table></body></html>';
    
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'landscape');
    $dompdf->render();
    
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="acordos_analitico_' . date('Y-m-d_H-i-s') . '.pdf"');
    
    echo $dompdf->output();
    exit;
}
?>