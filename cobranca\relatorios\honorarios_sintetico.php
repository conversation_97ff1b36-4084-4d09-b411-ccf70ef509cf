<?php
// Relatório Sintético de Honorários

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;
use Dompdf\Options;

// Construir WHERE clause baseado nos filtros
$where_conditions = [];
$params = [];

if (!empty($filtros['data_inicio'])) {
    $where_conditions[] = "h.data_recebimento >= ?";
    $params[] = $filtros['data_inicio'];
}

if (!empty($filtros['data_fim'])) {
    $where_conditions[] = "h.data_recebimento <= ?";
    $params[] = $filtros['data_fim'];
}

if (!empty($filtros['pa_id'])) {
    $where_conditions[] = "p.pa_id = ?";
    $params[] = $filtros['pa_id'];
}

if (!empty($filtros['advogado_id'])) {
    $where_conditions[] = "h.advogado_id = ?";
    $params[] = $filtros['advogado_id'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Query para resumo geral de honorários
$query_geral = "
    SELECT 
        COUNT(h.id) as total_honorarios,
        COALESCE(SUM(h.valor_honorario), 0) as valor_total_honorarios,
        COUNT(CASE WHEN h.status = 'PENDENTE' THEN 1 END) as honorarios_pendentes,
        COUNT(CASE WHEN h.status = 'PAGO' THEN 1 END) as honorarios_pagos,
        COALESCE(SUM(CASE WHEN h.status = 'PENDENTE' THEN h.valor_honorario ELSE 0 END), 0) as valor_pendente,
        COALESCE(SUM(CASE WHEN h.status = 'PAGO' THEN h.valor_honorario ELSE 0 END), 0) as valor_pago
    FROM cbp_honorarios h
    LEFT JOIN cbp_processos_judiciais p ON h.processo_id = p.id
    $where_clause
";

$stmt = $pdo->prepare($query_geral);
$stmt->execute($params);
$dados_geral = $stmt->fetch(PDO::FETCH_ASSOC);

// Query para honorários por tipo
$query_tipo = "
    SELECT 
        h.tipo,
        COUNT(h.id) as quantidade,
        COALESCE(SUM(h.valor_honorario), 0) as valor_total,
        COALESCE(AVG(h.valor_honorario), 0) as valor_medio
    FROM cbp_honorarios h
    LEFT JOIN cbp_processos_judiciais p ON h.processo_id = p.id
    $where_clause
    GROUP BY h.tipo
    ORDER BY valor_total DESC
";

$stmt = $pdo->prepare($query_tipo);
$stmt->execute($params);
$dados_tipo = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Query para honorários por advogado
$query_advogado = "
    SELECT 
        a.nome as advogado,
        COUNT(h.id) as quantidade,
        COALESCE(SUM(h.valor_honorario), 0) as valor_total,
        COALESCE(SUM(CASE WHEN h.status = 'PAGO' THEN h.valor_honorario ELSE 0 END), 0) as valor_pago,
        COALESCE(SUM(CASE WHEN h.status = 'PENDENTE' THEN h.valor_honorario ELSE 0 END), 0) as valor_pendente
    FROM cbp_honorarios h
    LEFT JOIN cbp_advogados a ON h.advogado_id = a.id
    LEFT JOIN cbp_processos_judiciais p ON h.processo_id = p.id
    $where_clause
    GROUP BY a.id, a.nome
    ORDER BY valor_total DESC
";

$stmt = $pdo->prepare($query_advogado);
$stmt->execute($params);
$dados_advogado = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Query para honorários por PA
$query_pa = "
    SELECT 
        pa.nome as pa,
        COUNT(h.id) as quantidade,
        COALESCE(SUM(h.valor_honorario), 0) as valor_total
    FROM cbp_honorarios h
    LEFT JOIN cbp_processos_judiciais p ON h.processo_id = p.id
    LEFT JOIN pontos_atendimento pa ON p.pa_id = pa.id
    $where_clause
    GROUP BY pa.id, pa.nome
    ORDER BY valor_total DESC
";

$stmt = $pdo->prepare($query_pa);
$stmt->execute($params);
$dados_pa = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Query para honorários por mês
$query_mes = "
    SELECT 
        DATE_FORMAT(h.data_recebimento, '%Y-%m') as mes,
        DATE_FORMAT(h.data_recebimento, '%m/%Y') as mes_formatado,
        COUNT(h.id) as quantidade,
        COALESCE(SUM(h.valor_honorario), 0) as valor_total
    FROM cbp_honorarios h
    LEFT JOIN cbp_processos_judiciais p ON h.processo_id = p.id
    $where_clause
    GROUP BY DATE_FORMAT(h.data_recebimento, '%Y-%m')
    ORDER BY mes DESC
    LIMIT 12
";

$stmt = $pdo->prepare($query_mes);
$stmt->execute($params);
$dados_mes = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Função para formatar valor
function formatarValor($valor) {
    return 'R$ ' . number_format($valor, 2, ',', '.');
}

// Gerar saída baseado no formato
if ($formato === 'html') {
    ?>
    <style>
        :root {
            --sicoob-turquesa: #00A091;
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-verde-claro: #C9D200;
            --sicoob-roxo: #49479D;
        }

        .contador-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            padding: 24px;
            text-align: center;
            border-left: 4px solid var(--sicoob-turquesa);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .contador-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .contador-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .contador-numero {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--sicoob-verde-escuro);
            margin-bottom: 8px;
        }

        .contador-label {
            color: #6c757d;
            font-size: 0.95rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .contador-sublabel {
            color: #6c757d;
            font-size: 0.8rem;
            margin-top: 5px;
        }

        .text-sicoob-primary { color: var(--sicoob-turquesa) !important; }
        .text-sicoob-success { color: var(--sicoob-verde-medio) !important; }
        .text-sicoob-purple { color: var(--sicoob-roxo) !important; }
        .text-sicoob-secondary { color: var(--sicoob-verde-escuro) !important; }

        .section-title {
            color: var(--sicoob-verde-escuro);
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--sicoob-turquesa);
        }

        .table-sicoob {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .table-sicoob thead th {
            background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-escuro) 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 12px 15px;
        }

        .table-sicoob tbody tr:hover {
            background-color: rgba(0, 160, 145, 0.05);
        }

        .table-sicoob tbody td {
            padding: 12px 15px;
            border-color: rgba(0, 160, 145, 0.1);
        }
    </style>

    <div class="relatorio-content">
        <!-- Cards de Resumo no estilo Contador com Ícone -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-money-check-alt contador-icon text-sicoob-primary"></i>
                    <div class="contador-numero"><?= number_format($dados_geral['total_honorarios']) ?></div>
                    <div class="contador-label">Total de Honorários</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-dollar-sign contador-icon text-sicoob-success"></i>
                    <div class="contador-numero"><?= formatarValor($dados_geral['valor_total_honorarios']) ?></div>
                    <div class="contador-label">Valor Total</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-hourglass-half contador-icon text-sicoob-purple"></i>
                    <div class="contador-numero"><?= number_format($dados_geral['honorarios_pendentes']) ?></div>
                    <div class="contador-label">Honorários Pendentes</div>
                    <div class="contador-sublabel"><?= formatarValor($dados_geral['valor_pendente']) ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-check-circle contador-icon text-sicoob-success"></i>
                    <div class="contador-numero"><?= number_format($dados_geral['honorarios_pagos']) ?></div>
                    <div class="contador-label">Honorários Pagos</div>
                    <div class="contador-sublabel"><?= formatarValor($dados_geral['valor_pago']) ?></div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h5 class="section-title">
                    <i class="fas fa-tags me-2"></i>Honorários por Tipo
                </h5>
                <div class="table-responsive">
                    <table class="table table-sicoob">
                        <thead>
                            <tr>
                                <th><i class="fas fa-tag me-2"></i>Tipo</th>
                                <th class="text-end"><i class="fas fa-hashtag me-2"></i>Quantidade</th>
                                <th class="text-end"><i class="fas fa-dollar-sign me-2"></i>Valor Total</th>
                                <th class="text-end"><i class="fas fa-calculator me-2"></i>Valor Médio</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($dados_tipo as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['tipo'] ?: 'Não informado') ?></td>
                                <td class="text-end"><?= number_format($row['quantidade']) ?></td>
                                <td class="text-end"><?= formatarValor($row['valor_total']) ?></td>
                                <td class="text-end"><?= formatarValor($row['valor_medio']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-md-6">
                <h5>Honorários por Advogado</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Advogado</th>
                                <th class="text-end">Quantidade</th>
                                <th class="text-end">Valor Total</th>
                                <th class="text-end">Valor Pago</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($dados_advogado as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['advogado'] ?: 'Não informado') ?></td>
                                <td class="text-end"><?= number_format($row['quantidade']) ?></td>
                                <td class="text-end"><?= formatarValor($row['valor_total']) ?></td>
                                <td class="text-end"><?= formatarValor($row['valor_pago']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <h5>Honorários por Ponto de Atendimento</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>PA</th>
                                <th class="text-end">Quantidade</th>
                                <th class="text-end">Valor Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($dados_pa as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['pa'] ?: 'Não informado') ?></td>
                                <td class="text-end"><?= number_format($row['quantidade']) ?></td>
                                <td class="text-end"><?= formatarValor($row['valor_total']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-md-6">
                <h5>Honorários por Mês (Últimos 12 meses)</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Mês/Ano</th>
                                <th class="text-end">Quantidade</th>
                                <th class="text-end">Valor Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($dados_mes as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['mes_formatado']) ?></td>
                                <td class="text-end"><?= number_format($row['quantidade']) ?></td>
                                <td class="text-end"><?= formatarValor($row['valor_total']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php
} elseif ($formato === 'excel') {
    // Limpar qualquer output buffer antes de gerar o Excel
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Honorários Sintético');
    
    // Cabeçalho
    $sheet->setCellValue('A1', 'RELATÓRIO SINTÉTICO DE HONORÁRIOS');
    $sheet->mergeCells('A1:D1');
    $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
    
    // Resumo geral
    $row = 3;
    $sheet->setCellValue('A' . $row, 'Total de Honorários:');
    $sheet->setCellValue('B' . $row, $dados_geral['total_honorarios']);
    $row++;
    $sheet->setCellValue('A' . $row, 'Valor Total:');
    $sheet->setCellValue('B' . $row, $dados_geral['valor_total_honorarios']);
    $row++;
    $sheet->setCellValue('A' . $row, 'Honorários Pendentes:');
    $sheet->setCellValue('B' . $row, $dados_geral['honorarios_pendentes']);
    $row++;
    $sheet->setCellValue('A' . $row, 'Honorários Pagos:');
    $sheet->setCellValue('B' . $row, $dados_geral['honorarios_pagos']);
    
    // Configurar download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="honorarios_sintetico_' . date('Y-m-d_H-i-s') . '.xlsx"');
    header('Cache-Control: max-age=0');
    
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
    
} elseif ($formato === 'pdf') {
    // Limpar qualquer output buffer antes de gerar o PDF
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);
    
    $dompdf = new Dompdf($options);
    
    $html = '
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; font-size: 12px; }
            .header { text-align: center; margin-bottom: 20px; }
            .totals { background: #f8f9fa; padding: 10px; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .text-right { text-align: right; }
            .section-title { font-weight: bold; margin-top: 20px; margin-bottom: 10px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>RELATÓRIO SINTÉTICO DE HONORÁRIOS</h1>
            <p>Gerado em: ' . date('d/m/Y H:i:s') . '</p>
        </div>
        
        <div class="totals">
            <p><strong>Total de Honorários:</strong> ' . number_format($dados_geral['total_honorarios']) . '</p>
            <p><strong>Valor Total:</strong> ' . formatarValor($dados_geral['valor_total_honorarios']) . '</p>
            <p><strong>Honorários Pendentes:</strong> ' . number_format($dados_geral['honorarios_pendentes']) . ' (' . formatarValor($dados_geral['valor_pendente']) . ')</p>
            <p><strong>Honorários Pagos:</strong> ' . number_format($dados_geral['honorarios_pagos']) . ' (' . formatarValor($dados_geral['valor_pago']) . ')</p>
        </div>
    </body>
    </html>';
    
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();
    
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="honorarios_sintetico_' . date('Y-m-d_H-i-s') . '.pdf"');
    
    echo $dompdf->output();
    exit;
}
?>