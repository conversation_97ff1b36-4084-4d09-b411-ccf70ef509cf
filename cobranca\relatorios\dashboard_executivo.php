<?php
// Dashboard Executivo

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;
use Dompdf\Options;

// Construir WHERE clause baseado nos filtros
$where_conditions = [];
$params = [];

if (!empty($filtros['data_inicio'])) {
    $where_conditions[] = "p.data_ajuizamento >= ?";
    $params[] = $filtros['data_inicio'];
}

if (!empty($filtros['data_fim'])) {
    $where_conditions[] = "p.data_ajuizamento <= ?";
    $params[] = $filtros['data_fim'];
}

if (!empty($filtros['pa_id'])) {
    $where_conditions[] = "p.pa_id = ?";
    $params[] = $filtros['pa_id'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Indicadores principais
$query_indicadores = "
    SELECT 
        COUNT(p.id) as total_processos,
        COALESCE(SUM(p.valor_ajuizado), 0) as valor_total_ajuizado,
        COUNT(CASE WHEN s.nome = 'VIGENTE' THEN 1 END) as processos_vigentes,
        COUNT(CASE WHEN s.nome = 'QUITADO' THEN 1 END) as processos_quitados,
        COUNT(CASE WHEN s.nome = 'ACORDO JUDICIAL' THEN 1 END) as processos_acordo,
        (SELECT COUNT(*) FROM cbp_acordos a 
         LEFT JOIN cbp_processos_judiciais p2 ON a.processo_id = p2.id 
         " . str_replace('p.', 'p2.', $where_clause) . ") as total_acordos,
        (SELECT COALESCE(SUM(a.valor_acordo), 0) FROM cbp_acordos a 
         LEFT JOIN cbp_processos_judiciais p2 ON a.processo_id = p2.id 
         " . str_replace('p.', 'p2.', $where_clause) . ") as valor_total_acordos,
        (SELECT COUNT(*) FROM cbp_honorarios h 
         LEFT JOIN cbp_processos_judiciais p2 ON h.processo_id = p2.id 
         " . str_replace('p.', 'p2.', $where_clause) . ") as total_honorarios,
        (SELECT COALESCE(SUM(h.valor_honorario), 0) FROM cbp_honorarios h 
         LEFT JOIN cbp_processos_judiciais p2 ON h.processo_id = p2.id 
         " . str_replace('p.', 'p2.', $where_clause) . ") as valor_total_honorarios
    FROM cbp_processos_judiciais p
    LEFT JOIN cbp_status_processo s ON p.status_id = s.id
    $where_clause
";

$stmt = $pdo->prepare($query_indicadores);
$stmt->execute($params);
$indicadores = $stmt->fetch(PDO::FETCH_ASSOC);

// Evolução mensal dos processos (últimos 12 meses)
$where_clause_evolucao = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) . ' AND p.data_ajuizamento >= DATE_SUB(NOW(), INTERVAL 12 MONTH)' : 'WHERE p.data_ajuizamento >= DATE_SUB(NOW(), INTERVAL 12 MONTH)';

$query_evolucao = "
    SELECT 
        DATE_FORMAT(p.data_ajuizamento, '%Y-%m') as mes,
        DATE_FORMAT(p.data_ajuizamento, '%m/%Y') as mes_formatado,
        COUNT(p.id) as quantidade,
        COALESCE(SUM(p.valor_ajuizado), 0) as valor_total
    FROM cbp_processos_judiciais p
    LEFT JOIN cbp_status_processo s ON p.status_id = s.id
    $where_clause_evolucao
    GROUP BY DATE_FORMAT(p.data_ajuizamento, '%Y-%m')
    ORDER BY mes DESC
    LIMIT 12
";

$stmt = $pdo->prepare($query_evolucao);
$stmt->execute($params);
$evolucao_mensal = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Top 5 PAs por valor
$query_top_pas = "
    SELECT 
        pa.nome as pa,
        COUNT(p.id) as quantidade,
        COALESCE(SUM(p.valor_ajuizado), 0) as valor_total
    FROM cbp_processos_judiciais p
    LEFT JOIN pontos_atendimento pa ON p.pa_id = pa.id
    LEFT JOIN cbp_status_processo s ON p.status_id = s.id
    $where_clause
    GROUP BY pa.id, pa.nome
    ORDER BY valor_total DESC
    LIMIT 5
";

$stmt = $pdo->prepare($query_top_pas);
$stmt->execute($params);
$top_pas = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Top 5 Advogados por quantidade
$query_top_advogados = "
    SELECT 
        a.nome as advogado,
        COUNT(p.id) as quantidade,
        COALESCE(SUM(p.valor_ajuizado), 0) as valor_total
    FROM cbp_processos_judiciais p
    LEFT JOIN cbp_advogados a ON p.advogado_id = a.id
    LEFT JOIN cbp_status_processo s ON p.status_id = s.id
    $where_clause
    GROUP BY a.id, a.nome
    ORDER BY quantidade DESC
    LIMIT 5
";

$stmt = $pdo->prepare($query_top_advogados);
$stmt->execute($params);
$top_advogados = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Parcelas vencendo nos próximos 30 dias
$query_vencimentos = "
    SELECT 
        COUNT(*) as parcelas_vencendo,
        COALESCE(SUM(pa.valor_parcela), 0) as valor_vencendo
    FROM cbp_parcelas_acordo pa
    LEFT JOIN cbp_acordos a ON pa.acordo_id = a.id
    LEFT JOIN cbp_processos_judiciais p ON a.processo_id = p.id
    WHERE pa.status = 'PENDENTE'
    AND pa.data_vencimento BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
    " . (!empty($where_conditions) ? 'AND ' . str_replace('p.', 'p.', implode(' AND ', $where_conditions)) : '')
;

$stmt = $pdo->prepare($query_vencimentos);
$stmt->execute($params);
$vencimentos = $stmt->fetch(PDO::FETCH_ASSOC);

// Função para formatar valor
function formatarValor($valor) {
    return 'R$ ' . number_format($valor, 2, ',', '.');
}

// Função para calcular percentual
function calcularPercentual($parte, $total) {
    return $total > 0 ? number_format(($parte / $total) * 100, 1) : '0';
}

// Gerar saída baseado no formato
if ($formato === 'html') {
    ?>
    <style>
        :root {
            --sicoob-turquesa: #00A091;
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-verde-claro: #C9D200;
            --sicoob-roxo: #49479D;
        }

        .contador-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            padding: 24px;
            text-align: center;
            border-left: 4px solid var(--sicoob-turquesa);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .contador-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .contador-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .contador-numero {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--sicoob-verde-escuro);
            margin-bottom: 8px;
        }

        .contador-label {
            color: #6c757d;
            font-size: 0.95rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .contador-sublabel {
            color: #6c757d;
            font-size: 0.8rem;
            margin-top: 5px;
        }

        .text-sicoob-primary { color: var(--sicoob-turquesa) !important; }
        .text-sicoob-success { color: var(--sicoob-verde-medio) !important; }
        .text-sicoob-purple { color: var(--sicoob-roxo) !important; }
        .text-sicoob-secondary { color: var(--sicoob-verde-escuro) !important; }

        .section-title {
            color: var(--sicoob-verde-escuro);
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--sicoob-turquesa);
        }

        .table-sicoob {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .table-sicoob thead th {
            background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-escuro) 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 12px 15px;
        }

        .table-sicoob tbody tr:hover {
            background-color: rgba(0, 160, 145, 0.05);
        }

        .table-sicoob tbody td {
            padding: 12px 15px;
            border-color: rgba(0, 160, 145, 0.1);
        }
    </style>

    <div class="relatorio-content">
        <!-- Indicadores Principais no estilo Contador com Ícone -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-gavel contador-icon text-sicoob-primary"></i>
                    <div class="contador-numero"><?= number_format($indicadores['total_processos']) ?></div>
                    <div class="contador-label">Processos</div>
                    <div class="contador-sublabel"><?= formatarValor($indicadores['valor_total_ajuizado']) ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-handshake contador-icon text-sicoob-success"></i>
                    <div class="contador-numero"><?= number_format($indicadores['total_acordos']) ?></div>
                    <div class="contador-label">Acordos</div>
                    <div class="contador-sublabel"><?= formatarValor($indicadores['valor_total_acordos']) ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-dollar-sign contador-icon text-sicoob-primary"></i>
                    <div class="contador-numero"><?= number_format($indicadores['total_honorarios']) ?></div>
                    <div class="contador-label">Honorários</div>
                    <div class="contador-sublabel"><?= formatarValor($indicadores['valor_total_honorarios']) ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-clock contador-icon text-sicoob-purple"></i>
                    <div class="contador-numero"><?= number_format($vencimentos['parcelas_vencendo']) ?></div>
                    <div class="contador-label">Vencimentos</div>
                    <div class="contador-sublabel"><?= formatarValor($vencimentos['valor_vencendo']) ?></div>
                </div>
            </div>
        </div>

        <!-- Status dos Processos -->
        <div class="row mb-4">
            <div class="col-md-12">
                <h5 class="section-title">
                    <i class="fas fa-chart-pie me-2"></i>Distribuição dos Processos por Status
                </h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="contador-card">
                            <i class="fas fa-play-circle contador-icon text-sicoob-success"></i>
                            <div class="contador-numero"><?= number_format($indicadores['processos_vigentes']) ?></div>
                            <div class="contador-label">Vigentes</div>
                            <div class="contador-sublabel"><?= calcularPercentual($indicadores['processos_vigentes'], $indicadores['total_processos']) ?>% do total</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="contador-card">
                            <i class="fas fa-check-circle contador-icon text-sicoob-primary"></i>
                            <div class="contador-numero"><?= number_format($indicadores['processos_quitados']) ?></div>
                            <div class="contador-label">Quitados</div>
                            <div class="contador-sublabel"><?= calcularPercentual($indicadores['processos_quitados'], $indicadores['total_processos']) ?>% do total</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="contador-card">
                            <i class="fas fa-handshake contador-icon text-sicoob-purple"></i>
                            <div class="contador-numero"><?= number_format($indicadores['processos_acordo']) ?></div>
                            <div class="contador-label">Acordo Judicial</div>
                            <div class="contador-sublabel"><?= calcularPercentual($indicadores['processos_acordo'], $indicadores['total_processos']) ?>% do total</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rankings -->
        <div class="row">
            <div class="col-md-4">
                <h5 class="section-title">
                    <i class="fas fa-trophy me-2"></i>Top 5 PAs por Valor
                </h5>
                <div class="table-responsive">
                    <table class="table table-sicoob table-sm">
                        <thead>
                            <tr>
                                <th><i class="fas fa-building me-2"></i>PA</th>
                                <th class="text-end"><i class="fas fa-hashtag me-2"></i>Qtd</th>
                                <th class="text-end">Valor</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_pas as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['pa'] ?: 'N/I') ?></td>
                                <td class="text-end"><?= number_format($row['quantidade']) ?></td>
                                <td class="text-end"><?= formatarValor($row['valor_total']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-md-4">
                <h5>Top 5 Advogados por Quantidade</h5>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>Advogado</th>
                                <th class="text-end">Qtd</th>
                                <th class="text-end">Valor</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_advogados as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['advogado'] ?: 'N/I') ?></td>
                                <td class="text-end"><?= number_format($row['quantidade']) ?></td>
                                <td class="text-end"><?= formatarValor($row['valor_total']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-md-4">
                <h5>Evolução Mensal (Últimos 12 meses)</h5>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>Mês</th>
                                <th class="text-end">Qtd</th>
                                <th class="text-end">Valor</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_reverse($evolucao_mensal) as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['mes_formatado']) ?></td>
                                <td class="text-end"><?= number_format($row['quantidade']) ?></td>
                                <td class="text-end"><?= formatarValor($row['valor_total']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php
} elseif ($formato === 'pdf') {
    // Limpar qualquer output buffer antes de gerar o PDF
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);
    
    $dompdf = new Dompdf($options);
    
    $html = '
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; font-size: 12px; }
            .header { text-align: center; margin-bottom: 20px; }
            .indicators { display: flex; justify-content: space-between; margin-bottom: 20px; }
            .indicator { text-align: center; padding: 10px; background: #f8f9fa; border-radius: 5px; width: 23%; }
            .indicator h3 { margin: 5px 0; color: #003641; }
            .indicator small { color: #666; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .text-right { text-align: right; }
            .section-title { font-weight: bold; margin-top: 20px; margin-bottom: 10px; color: #003641; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>DASHBOARD EXECUTIVO</h1>
            <p>Gerado em: ' . date('d/m/Y H:i:s') . '</p>
        </div>
        
        <div class="indicators">
            <div class="indicator">
                <h3>' . number_format($indicadores['total_processos']) . '</h3>
                <small>Processos<br>' . formatarValor($indicadores['valor_total_ajuizado']) . '</small>
            </div>
            <div class="indicator">
                <h3>' . number_format($indicadores['total_acordos']) . '</h3>
                <small>Acordos<br>' . formatarValor($indicadores['valor_total_acordos']) . '</small>
            </div>
            <div class="indicator">
                <h3>' . number_format($indicadores['total_honorarios']) . '</h3>
                <small>Honorários<br>' . formatarValor($indicadores['valor_total_honorarios']) . '</small>
            </div>
            <div class="indicator">
                <h3>' . number_format($vencimentos['parcelas_vencendo']) . '</h3>
                <small>Vencimentos<br>' . formatarValor($vencimentos['valor_vencendo']) . '</small>
            </div>
        </div>
        
        <div class="section-title">TOP 5 PAs POR VALOR</div>
        <table>
            <thead>
                <tr>
                    <th>PA</th>
                    <th class="text-right">Quantidade</th>
                    <th class="text-right">Valor Total</th>
                </tr>
            </thead>
            <tbody>';
    
    foreach ($top_pas as $row) {
        $html .= '<tr>
            <td>' . htmlspecialchars($row['pa'] ?: 'N/I') . '</td>
            <td class="text-right">' . number_format($row['quantidade']) . '</td>
            <td class="text-right">' . formatarValor($row['valor_total']) . '</td>
        </tr>';
    }
    
    $html .= '</tbody></table></body></html>';
    
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();
    
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="dashboard_executivo_' . date('Y-m-d_H-i-s') . '.pdf"');
    
    echo $dompdf->output();
    exit;
}
?>