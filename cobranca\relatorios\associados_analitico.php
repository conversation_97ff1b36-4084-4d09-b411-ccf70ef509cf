<?php
// Relatório Analítico de Associados

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;
use Dompdf\Options;

// Construir WHERE clause baseado nos filtros
$where_conditions = [];
$params = [];

if (!empty($filtros['pa_id'])) {
    $where_conditions[] = "a.pa_id = ?";
    $params[] = $filtros['pa_id'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Query principal
$query = "
    SELECT 
        a.id,
        a.nome,
        a.cpf_cnpj,
        a.numero_contato,
        a.ativo,
        pa.nome as pa_nome,
        DATE_FORMAT(a.created_at, '%d/%m/%Y') as data_cadastro_formatada,
        DATE_FORMAT(a.updated_at, '%d/%m/%Y') as data_atualizacao_formatada,
        (SELECT COUNT(*) FROM cbp_processos_judiciais p WHERE p.associado_documento COLLATE utf8mb4_general_ci = a.cpf_cnpj COLLATE utf8mb4_general_ci) as total_processos,
        (SELECT COALESCE(SUM(p.valor_ajuizado), 0) FROM cbp_processos_judiciais p WHERE p.associado_documento COLLATE utf8mb4_general_ci = a.cpf_cnpj COLLATE utf8mb4_general_ci) as valor_total_processos,
        (SELECT COUNT(*) FROM cbp_processos_judiciais p 
         INNER JOIN cbp_acordos ac ON p.id = ac.processo_id 
         WHERE p.associado_documento COLLATE utf8mb4_general_ci = a.cpf_cnpj COLLATE utf8mb4_general_ci) as total_acordos,
        (SELECT COALESCE(SUM(ac.valor_acordo), 0) FROM cbp_processos_judiciais p 
         INNER JOIN cbp_acordos ac ON p.id = ac.processo_id 
         WHERE p.associado_documento COLLATE utf8mb4_general_ci = a.cpf_cnpj COLLATE utf8mb4_general_ci) as valor_total_acordos
    FROM cbp_associados a
    LEFT JOIN pontos_atendimento pa ON a.pa_id = pa.id
    $where_clause
    ORDER BY a.nome ASC
    LIMIT 1000
";

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$dados = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Função para formatar valor
function formatarValor($valor) {
    return $valor ? 'R$ ' . number_format($valor, 2, ',', '.') : '-';
}

// Função para formatar documento
function formatarDocumento($doc) {
    if (!$doc) return '-';
    $doc = preg_replace('/[^0-9]/', '', $doc);
    if (strlen($doc) === 11) {
        return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $doc);
    } else if (strlen($doc) === 14) {
        return preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $doc);
    }
    return $doc;
}

// Função para formatar telefone
function formatarTelefone($telefone) {
    if (!$telefone) return '-';
    $telefone = preg_replace('/[^0-9]/', '', $telefone);
    if (strlen($telefone) === 11) {
        return preg_replace('/(\d{2})(\d{5})(\d{4})/', '($1) $2-$3', $telefone);
    } else if (strlen($telefone) === 10) {
        return preg_replace('/(\d{2})(\d{4})(\d{4})/', '($1) $2-$3', $telefone);
    }
    return $telefone;
}

// Gerar saída baseado no formato
if ($formato === 'html') {
    ?>
    <div class="relatorio-content">
        <div class="mb-3">
            <strong>Total de registros:</strong> <?= count($dados) ?>
            <?php if (count($dados) >= 1000): ?>
                <span class="text-warning">(Limitado a 1000 registros)</span>
            <?php endif; ?>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>Nome</th>
                        <th>Documento</th>
                        <th>PA</th>
                        <th>Contato</th>
                        <th>Status</th>
                        <th class="text-center">Processos</th>
                        <th class="text-end">Valor Processos</th>
                        <th>Data Cadastro</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($dados as $row): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['nome'] ?: '-') ?></td>
                        <td><?= formatarDocumento($row['cpf_cnpj']) ?></td>
                        <td><?= htmlspecialchars($row['pa_nome'] ?: '-') ?></td>
                        <td><?= formatarTelefone($row['numero_contato']) ?></td>
                        <td>
                            <?php if ($row['ativo']): ?>
                                <span class="badge bg-success">Ativo</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Inativo</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center">
                            <?php if ($row['total_processos'] > 0): ?>
                                <span class="badge bg-primary"><?= $row['total_processos'] ?></span>
                            <?php else: ?>
                                <span class="text-muted">0</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-end"><?= formatarValor($row['valor_total_processos']) ?></td>
                        <td><?= $row['data_cadastro_formatada'] ?: '-' ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <?php if (empty($dados)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Nenhum associado encontrado com os filtros aplicados.
        </div>
        <?php endif; ?>
    </div>
    <?php
} elseif ($formato === 'excel') {
    // Limpar qualquer output buffer antes de gerar o Excel
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Associados Analítico');
    
    // Cabeçalhos
    $headers = [
        'A' => 'Nome',
        'B' => 'CPF/CNPJ',
        'C' => 'PA',
        'D' => 'Contato',
        'E' => 'Status',
        'F' => 'Total Processos',
        'G' => 'Valor Total Processos',
        'H' => 'Total Acordos',
        'I' => 'Valor Total Acordos',
        'J' => 'Data Cadastro',
        'K' => 'Data Atualização'
    ];
    
    // Definir cabeçalhos
    foreach ($headers as $col => $header) {
        $sheet->setCellValue($col . '1', $header);
    }
    $sheet->getStyle('A1:K1')->getFont()->setBold(true);
    
    // Preencher dados
    $row = 2;
    foreach ($dados as $data) {
        $sheet->setCellValue('A' . $row, $data['nome'] ?: '');
        $sheet->setCellValue('B' . $row, $data['cpf_cnpj'] ?: '');
        $sheet->setCellValue('C' . $row, $data['pa_nome'] ?: '');
        $sheet->setCellValue('D' . $row, $data['numero_contato'] ?: '');
        $sheet->setCellValue('E' . $row, $data['ativo'] ? 'Ativo' : 'Inativo');
        $sheet->setCellValue('F' . $row, $data['total_processos'] ?: 0);
        $sheet->setCellValue('G' . $row, $data['valor_total_processos'] ?: 0);
        $sheet->setCellValue('H' . $row, $data['total_acordos'] ?: 0);
        $sheet->setCellValue('I' . $row, $data['valor_total_acordos'] ?: 0);
        $sheet->setCellValue('J' . $row, $data['data_cadastro_formatada'] ?: '');
        $sheet->setCellValue('K' . $row, $data['data_atualizacao_formatada'] ?: '');
        $row++;
    }
    
    // Auto-ajustar colunas
    foreach (range('A', 'K') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    
    // Configurar download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="associados_analitico_' . date('Y-m-d_H-i-s') . '.xlsx"');
    header('Cache-Control: max-age=0');
    
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
    
} elseif ($formato === 'pdf') {
    // Limpar qualquer output buffer antes de gerar o PDF
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);
    
    $dompdf = new Dompdf($options);
    
    $html = '
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; font-size: 9px; }
            .header { text-align: center; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; }
            th, td { border: 1px solid #ddd; padding: 3px; text-align: left; font-size: 8px; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .text-right { text-align: right; }
            .text-center { text-align: center; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>RELATÓRIO ANALÍTICO DE ASSOCIADOS</h1>
            <p>Gerado em: ' . date('d/m/Y H:i:s') . '</p>
            <p>Total de registros: ' . count($dados) . '</p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Nome</th>
                    <th>Documento</th>
                    <th>PA</th>
                    <th>Status</th>
                    <th class="text-center">Proc.</th>
                    <th>Data Cad.</th>
                </tr>
            </thead>
            <tbody>';
    
    foreach ($dados as $row) {
        $html .= '<tr>
            <td>' . htmlspecialchars(substr($row['nome'] ?: '-', 0, 25)) . '</td>
            <td>' . formatarDocumento($row['cpf_cnpj']) . '</td>
            <td>' . htmlspecialchars($row['pa_nome'] ?: '-') . '</td>
            <td>' . ($row['ativo'] ? 'Ativo' : 'Inativo') . '</td>
            <td class="text-center">' . ($row['total_processos'] ?: '0') . '</td>
            <td>' . ($row['data_cadastro_formatada'] ?: '-') . '</td>
        </tr>';
    }
    
    $html .= '</tbody></table></body></html>';
    
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'landscape');
    $dompdf->render();
    
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="associados_analitico_' . date('Y-m-d_H-i-s') . '.pdf"');
    
    echo $dompdf->output();
    exit;
}
?>