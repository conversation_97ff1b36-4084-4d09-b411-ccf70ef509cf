<?php
// Relatório Sintético de Associados

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;
use Dompdf\Options;

// Construir WHERE clause baseado nos filtros
$where_conditions = [];
$params = [];

if (!empty($filtros['pa_id'])) {
    $where_conditions[] = "a.pa_id = ?";
    $params[] = $filtros['pa_id'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Query para resumo geral de associados
$query_geral = "
    SELECT 
        COUNT(a.id) as total_associados,
        COUNT(CASE WHEN a.ativo = 1 THEN 1 END) as associados_ativos,
        COUNT(CASE WHEN a.ativo = 0 THEN 1 END) as associados_inativos,
        COUNT(CASE WHEN LENGTH(REPLACE(REPLACE(REPLACE(a.cpf_cnpj, '.', ''), '-', ''), '/', '')) = 11 THEN 1 END) as pessoas_fisicas,
        COUNT(CASE WHEN LENGTH(REPLACE(REPLACE(REPLACE(a.cpf_cnpj, '.', ''), '-', ''), '/', '')) = 14 THEN 1 END) as pessoas_juridicas
    FROM cbp_associados a
    $where_clause
";

$stmt = $pdo->prepare($query_geral);
$stmt->execute($params);
$dados_geral = $stmt->fetch(PDO::FETCH_ASSOC);

// Query para associados por PA
$query_pa = "
    SELECT 
        pa.nome as pa,
        COUNT(a.id) as total,
        COUNT(CASE WHEN a.ativo = 1 THEN 1 END) as ativos,
        COUNT(CASE WHEN a.ativo = 0 THEN 1 END) as inativos
    FROM cbp_associados a
    LEFT JOIN pontos_atendimento pa ON a.pa_id = pa.id
    $where_clause
    GROUP BY pa.id, pa.nome
    ORDER BY total DESC
";

$stmt = $pdo->prepare($query_pa);
$stmt->execute($params);
$dados_pa = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Query para associados com processos
$query_processos = "
    SELECT 
        COUNT(DISTINCT a.id) as associados_com_processos,
        COUNT(p.id) as total_processos,
        COALESCE(SUM(p.valor_ajuizado), 0) as valor_total_processos
    FROM cbp_associados a
    INNER JOIN cbp_processos_judiciais p ON a.cpf_cnpj COLLATE utf8mb4_general_ci = p.associado_documento COLLATE utf8mb4_general_ci
    $where_clause
";

$stmt = $pdo->prepare($query_processos);
$stmt->execute($params);
$dados_processos = $stmt->fetch(PDO::FETCH_ASSOC);

// Query para associados com acordos
$query_acordos = "
    SELECT 
        COUNT(DISTINCT a.id) as associados_com_acordos,
        COUNT(ac.id) as total_acordos,
        COALESCE(SUM(ac.valor_acordo), 0) as valor_total_acordos
    FROM cbp_associados a
    INNER JOIN cbp_processos_judiciais p ON a.cpf_cnpj COLLATE utf8mb4_general_ci = p.associado_documento COLLATE utf8mb4_general_ci
    INNER JOIN cbp_acordos ac ON p.id = ac.processo_id
    $where_clause
";

$stmt = $pdo->prepare($query_acordos);
$stmt->execute($params);
$dados_acordos = $stmt->fetch(PDO::FETCH_ASSOC);

// Query para associados cadastrados por mês (últimos 12 meses)
$where_clause_mes = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) . ' AND a.created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)' : 'WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)';

$query_mes = "
    SELECT 
        DATE_FORMAT(a.created_at, '%Y-%m') as mes,
        DATE_FORMAT(a.created_at, '%m/%Y') as mes_formatado,
        COUNT(a.id) as quantidade
    FROM cbp_associados a
    $where_clause_mes
    GROUP BY DATE_FORMAT(a.created_at, '%Y-%m')
    ORDER BY mes DESC
";

$stmt = $pdo->prepare($query_mes);
$stmt->execute($params);
$dados_mes = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Função para formatar valor
function formatarValor($valor) {
    return 'R$ ' . number_format($valor, 2, ',', '.');
}

// Gerar saída baseado no formato
if ($formato === 'html') {
    ?>
    <div class="relatorio-content">
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h5>Total de Associados</h5>
                        <h2><?= number_format($dados_geral['total_associados']) ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h5>Associados Ativos</h5>
                        <h2><?= number_format($dados_geral['associados_ativos']) ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h5>Pessoas Físicas</h5>
                        <h2><?= number_format($dados_geral['pessoas_fisicas']) ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h5>Pessoas Jurídicas</h5>
                        <h2><?= number_format($dados_geral['pessoas_juridicas']) ?></h2>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Associados com Processos</h5>
                        <h3 class="text-primary"><?= number_format($dados_processos['associados_com_processos']) ?></h3>
                        <p class="mb-1"><strong>Total de Processos:</strong> <?= number_format($dados_processos['total_processos']) ?></p>
                        <p class="mb-0"><strong>Valor Total:</strong> <?= formatarValor($dados_processos['valor_total_processos']) ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Associados com Acordos</h5>
                        <h3 class="text-success"><?= number_format($dados_acordos['associados_com_acordos']) ?></h3>
                        <p class="mb-1"><strong>Total de Acordos:</strong> <?= number_format($dados_acordos['total_acordos']) ?></p>
                        <p class="mb-0"><strong>Valor Total:</strong> <?= formatarValor($dados_acordos['valor_total_acordos']) ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Associados Inativos</h5>
                        <h3 class="text-danger"><?= number_format($dados_geral['associados_inativos']) ?></h3>
                        <p class="text-muted">
                            <?= $dados_geral['total_associados'] > 0 ? number_format(($dados_geral['associados_inativos'] / $dados_geral['total_associados']) * 100, 1) : 0 ?>% do total
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h5>Associados por Ponto de Atendimento</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>PA</th>
                                <th class="text-end">Total</th>
                                <th class="text-end">Ativos</th>
                                <th class="text-end">Inativos</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($dados_pa as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['pa'] ?: 'Não informado') ?></td>
                                <td class="text-end"><?= number_format($row['total']) ?></td>
                                <td class="text-end text-success"><?= number_format($row['ativos']) ?></td>
                                <td class="text-end text-danger"><?= number_format($row['inativos']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-md-6">
                <h5>Associados Cadastrados por Mês (Últimos 12 meses)</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Mês/Ano</th>
                                <th class="text-end">Quantidade</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($dados_mes)): ?>
                            <tr>
                                <td colspan="2" class="text-center text-muted">Nenhum cadastro nos últimos 12 meses</td>
                            </tr>
                            <?php else: ?>
                                <?php foreach ($dados_mes as $row): ?>
                                <tr>
                                    <td><?= htmlspecialchars($row['mes_formatado']) ?></td>
                                    <td class="text-end"><?= number_format($row['quantidade']) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php
} elseif ($formato === 'excel') {
    // Limpar qualquer output buffer antes de gerar o Excel
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Associados Sintético');
    
    // Cabeçalho
    $sheet->setCellValue('A1', 'RELATÓRIO SINTÉTICO DE ASSOCIADOS');
    $sheet->mergeCells('A1:D1');
    $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
    
    // Resumo geral
    $row = 3;
    $sheet->setCellValue('A' . $row, 'Total de Associados:');
    $sheet->setCellValue('B' . $row, $dados_geral['total_associados']);
    $row++;
    $sheet->setCellValue('A' . $row, 'Associados Ativos:');
    $sheet->setCellValue('B' . $row, $dados_geral['associados_ativos']);
    $row++;
    $sheet->setCellValue('A' . $row, 'Associados Inativos:');
    $sheet->setCellValue('B' . $row, $dados_geral['associados_inativos']);
    $row++;
    $sheet->setCellValue('A' . $row, 'Pessoas Físicas:');
    $sheet->setCellValue('B' . $row, $dados_geral['pessoas_fisicas']);
    $row++;
    $sheet->setCellValue('A' . $row, 'Pessoas Jurídicas:');
    $sheet->setCellValue('B' . $row, $dados_geral['pessoas_juridicas']);
    
    // Configurar download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="associados_sintetico_' . date('Y-m-d_H-i-s') . '.xlsx"');
    header('Cache-Control: max-age=0');
    
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
    
} elseif ($formato === 'pdf') {
    // Limpar qualquer output buffer antes de gerar o PDF
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);
    
    $dompdf = new Dompdf($options);
    
    $html = '
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; font-size: 12px; }
            .header { text-align: center; margin-bottom: 20px; }
            .totals { background: #f8f9fa; padding: 10px; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .text-right { text-align: right; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>RELATÓRIO SINTÉTICO DE ASSOCIADOS</h1>
            <p>Gerado em: ' . date('d/m/Y H:i:s') . '</p>
        </div>
        
        <div class="totals">
            <p><strong>Total de Associados:</strong> ' . number_format($dados_geral['total_associados']) . '</p>
            <p><strong>Associados Ativos:</strong> ' . number_format($dados_geral['associados_ativos']) . '</p>
            <p><strong>Associados Inativos:</strong> ' . number_format($dados_geral['associados_inativos']) . '</p>
            <p><strong>Pessoas Físicas:</strong> ' . number_format($dados_geral['pessoas_fisicas']) . '</p>
            <p><strong>Pessoas Jurídicas:</strong> ' . number_format($dados_geral['pessoas_juridicas']) . '</p>
        </div>
    </body>
    </html>';
    
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();
    
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="associados_sintetico_' . date('Y-m-d_H-i-s') . '.pdf"');
    
    echo $dompdf->output();
    exit;
}
?>