<?php
// Relatório de Vencimentos Próximos

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;
use Dompdf\Options;

// Construir WHERE clause baseado nos filtros
$where_conditions = ['pa.status = "PENDENTE"'];
$params = [];

// Definir período de vencimento (próximos 30 dias por padrão)
$dias_vencimento = 30;
$where_conditions[] = "pa.data_vencimento BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)";
$params[] = $dias_vencimento;

if (!empty($filtros['pa_id'])) {
    $where_conditions[] = "p.pa_id = ?";
    $params[] = $filtros['pa_id'];
}

if (!empty($filtros['advogado_id'])) {
    $where_conditions[] = "p.advogado_id = ?";
    $params[] = $filtros['advogado_id'];
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Query principal
$query = "
    SELECT 
        pa.id as parcela_id,
        pa.numero_parcela,
        pa.valor_parcela,
        pa.data_vencimento,
        DATE_FORMAT(pa.data_vencimento, '%d/%m/%Y') as data_vencimento_formatada,
        DATEDIFF(pa.data_vencimento, CURDATE()) as dias_para_vencimento,
        a.numero_repactuacao,
        a.valor_acordo,
        p.numero_processo,
        p.associado_nome,
        p.associado_documento,
        pt.nome as pa_nome,
        adv.nome as advogado_nome,
        CASE 
            WHEN pa.status = 'ATRASADO' OR DATEDIFF(pa.data_vencimento, CURDATE()) < 0 THEN 'VENCIDO'
            WHEN DATEDIFF(pa.data_vencimento, CURDATE()) <= 7 THEN 'VENCE_SEMANA'
            WHEN DATEDIFF(pa.data_vencimento, CURDATE()) <= 15 THEN 'VENCE_QUINZENA'
            ELSE 'VENCE_MES'
        END as urgencia
    FROM cbp_parcelas_acordo pa
    LEFT JOIN cbp_acordos a ON pa.acordo_id = a.id
    LEFT JOIN cbp_processos_judiciais p ON a.processo_id = p.id
    LEFT JOIN pontos_atendimento pt ON p.pa_id = pt.id
    LEFT JOIN cbp_advogados adv ON p.advogado_id = adv.id
    $where_clause
    ORDER BY pa.data_vencimento ASC, p.associado_nome ASC
    LIMIT 1000
";

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$dados = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Resumo por urgência
$resumo_urgencia = [
    'VENCIDO' => ['count' => 0, 'valor' => 0],
    'VENCE_SEMANA' => ['count' => 0, 'valor' => 0],
    'VENCE_QUINZENA' => ['count' => 0, 'valor' => 0],
    'VENCE_MES' => ['count' => 0, 'valor' => 0]
];

foreach ($dados as $row) {
    $urgencia = $row['urgencia'];
    $resumo_urgencia[$urgencia]['count']++;
    $resumo_urgencia[$urgencia]['valor'] += $row['valor_parcela'];
}

// Função para formatar valor
function formatarValor($valor) {
    return 'R$ ' . number_format($valor, 2, ',', '.');
}

// Função para formatar documento
function formatarDocumento($doc) {
    if (!$doc) return '-';
    $doc = preg_replace('/[^0-9]/', '', $doc);
    if (strlen($doc) === 11) {
        return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $doc);
    } else if (strlen($doc) === 14) {
        return preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $doc);
    }
    return $doc;
}

// Função para obter cor da urgência
function getUrgenciaColor($urgencia) {
    switch ($urgencia) {
        case 'VENCIDO': return 'danger';
        case 'VENCE_SEMANA': return 'warning';
        case 'VENCE_QUINZENA': return 'info';
        case 'VENCE_MES': return 'secondary';
        default: return 'secondary';
    }
}

// Função para obter texto da urgência
function getUrgenciaTexto($urgencia) {
    switch ($urgencia) {
        case 'VENCIDO': return 'Vencido';
        case 'VENCE_SEMANA': return 'Vence em 7 dias';
        case 'VENCE_QUINZENA': return 'Vence em 15 dias';
        case 'VENCE_MES': return 'Vence em 30 dias';
        default: return 'Indefinido';
    }
}

// Gerar saída baseado no formato
if ($formato === 'html') {
    ?>
    <style>
        :root {
            --sicoob-turquesa: #00A091;
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-verde-claro: #C9D200;
            --sicoob-roxo: #49479D;
        }

        .contador-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            padding: 24px;
            text-align: center;
            border-left: 4px solid var(--sicoob-turquesa);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .contador-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .contador-card.urgente {
            border-left-color: #dc3545;
        }

        .contador-card.atencao {
            border-left-color: #ffc107;
        }

        .contador-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .contador-numero {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--sicoob-verde-escuro);
            margin-bottom: 8px;
        }

        .contador-label {
            color: #6c757d;
            font-size: 0.95rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .contador-sublabel {
            color: #6c757d;
            font-size: 0.8rem;
            margin-top: 5px;
        }

        .text-sicoob-primary { color: var(--sicoob-turquesa) !important; }
        .text-sicoob-success { color: var(--sicoob-verde-medio) !important; }
        .text-sicoob-purple { color: var(--sicoob-roxo) !important; }
        .text-sicoob-secondary { color: var(--sicoob-verde-escuro) !important; }
        .text-danger { color: #dc3545 !important; }
        .text-warning { color: #ffc107 !important; }

        .section-title {
            color: var(--sicoob-verde-escuro);
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--sicoob-turquesa);
        }

        .table-sicoob {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .table-sicoob thead th {
            background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-escuro) 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 12px 15px;
        }

        .table-sicoob tbody tr:hover {
            background-color: rgba(0, 160, 145, 0.05);
        }

        .table-sicoob tbody td {
            padding: 12px 15px;
            border-color: rgba(0, 160, 145, 0.1);
        }
    </style>

    <div class="relatorio-content">
        <!-- Resumo por Urgência no estilo Contador com Ícone -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="contador-card urgente">
                    <i class="fas fa-exclamation-triangle contador-icon text-danger"></i>
                    <div class="contador-numero"><?= number_format($resumo_urgencia['VENCIDO']['count']) ?></div>
                    <div class="contador-label">Vencidos</div>
                    <div class="contador-sublabel"><?= formatarValor($resumo_urgencia['VENCIDO']['valor']) ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="contador-card atencao">
                    <i class="fas fa-clock contador-icon text-warning"></i>
                    <div class="contador-numero"><?= number_format($resumo_urgencia['VENCE_SEMANA']['count']) ?></div>
                    <div class="contador-label">7 Dias</div>
                    <div class="contador-sublabel"><?= formatarValor($resumo_urgencia['VENCE_SEMANA']['valor']) ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-calendar contador-icon text-sicoob-primary"></i>
                    <div class="contador-numero"><?= number_format($resumo_urgencia['VENCE_QUINZENA']['count']) ?></div>
                    <div class="contador-label">15 Dias</div>
                    <div class="contador-sublabel"><?= formatarValor($resumo_urgencia['VENCE_QUINZENA']['valor']) ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-calendar-alt contador-icon text-sicoob-purple"></i>
                    <div class="contador-numero"><?= number_format($resumo_urgencia['VENCE_MES']['count']) ?></div>
                    <div class="contador-label">30 Dias</div>
                    <div class="contador-sublabel"><?= formatarValor($resumo_urgencia['VENCE_MES']['valor']) ?></div>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <h5 class="section-title">
                <i class="fas fa-list me-2"></i>Detalhamento das Parcelas
            </h5>
            <p><strong>Total de parcelas:</strong> <?= count($dados) ?>
            <?php if (count($dados) >= 1000): ?>
                <span class="text-warning">(Limitado a 1000 registros)</span>
            <?php endif; ?></p>
        </div>

        <div class="table-responsive">
            <table class="table table-sicoob table-sm">
                <thead>
                    <tr>
                        <th><i class="fas fa-exclamation-circle me-2"></i>Urgência</th>
                        <th><i class="fas fa-user me-2"></i>Associado</th>
                        <th><i class="fas fa-file-contract me-2"></i>Nº Repactuação</th>
                        <th><i class="fas fa-building me-2"></i>PA</th>
                        <th class="text-center"><i class="fas fa-hashtag me-2"></i>Parcela</th>
                        <th class="text-end"><i class="fas fa-dollar-sign me-2"></i>Valor</th>
                        <th><i class="fas fa-calendar me-2"></i>Data Vencimento</th>
                        <th class="text-center"><i class="fas fa-clock me-2"></i>Dias</th>
                        <th><i class="fas fa-user-tie me-2"></i>Advogado</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($dados as $row): ?>
                    <tr>
                        <td>
                            <span class="badge bg-<?= getUrgenciaColor($row['urgencia']) ?>">
                                <?= getUrgenciaTexto($row['urgencia']) ?>
                            </span>
                        </td>
                        <td><?= htmlspecialchars($row['associado_nome'] ?: '-') ?></td>
                        <td><?= htmlspecialchars($row['numero_repactuacao'] ?: '-') ?></td>
                        <td><?= htmlspecialchars($row['pa_nome'] ?: '-') ?></td>
                        <td class="text-center"><?= $row['numero_parcela'] ?: '-' ?></td>
                        <td class="text-end"><?= formatarValor($row['valor_parcela']) ?></td>
                        <td><?= $row['data_vencimento_formatada'] ?: '-' ?></td>
                        <td class="text-center">
                            <?php if ($row['dias_para_vencimento'] < 0): ?>
                                <span class="text-danger"><?= abs($row['dias_para_vencimento']) ?> dias atrás</span>
                            <?php elseif ($row['dias_para_vencimento'] == 0): ?>
                                <span class="text-warning">Hoje</span>
                            <?php else: ?>
                                <span class="text-info"><?= $row['dias_para_vencimento'] ?> dias</span>
                            <?php endif; ?>
                        </td>
                        <td><?= htmlspecialchars($row['advogado_nome'] ?: '-') ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <?php if (empty($dados)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            Nenhuma parcela com vencimento nos próximos <?= $dias_vencimento ?> dias.
        </div>
        <?php endif; ?>
    </div>
    <?php
} elseif ($formato === 'excel') {
    // Limpar qualquer output buffer antes de gerar o Excel
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Vencimentos Próximos');
    
    // Cabeçalhos
    $headers = [
        'A' => 'Urgência',
        'B' => 'Associado',
        'C' => 'Documento',
        'D' => 'Nº Repactuação',
        'E' => 'Nº Processo',
        'F' => 'PA',
        'G' => 'Advogado',
        'H' => 'Nº Parcela',
        'I' => 'Valor Parcela',
        'J' => 'Data Vencimento',
        'K' => 'Dias para Vencimento',
        'L' => 'Valor Acordo'
    ];
    
    // Definir cabeçalhos
    foreach ($headers as $col => $header) {
        $sheet->setCellValue($col . '1', $header);
    }
    $sheet->getStyle('A1:L1')->getFont()->setBold(true);
    
    // Preencher dados
    $row = 2;
    foreach ($dados as $data) {
        $sheet->setCellValue('A' . $row, getUrgenciaTexto($data['urgencia']));
        $sheet->setCellValue('B' . $row, $data['associado_nome'] ?: '');
        $sheet->setCellValue('C' . $row, $data['associado_documento'] ?: '');
        $sheet->setCellValue('D' . $row, $data['numero_repactuacao'] ?: '');
        $sheet->setCellValue('E' . $row, $data['numero_processo'] ?: '');
        $sheet->setCellValue('F' . $row, $data['pa_nome'] ?: '');
        $sheet->setCellValue('G' . $row, $data['advogado_nome'] ?: '');
        $sheet->setCellValue('H' . $row, $data['numero_parcela'] ?: '');
        $sheet->setCellValue('I' . $row, $data['valor_parcela'] ?: 0);
        $sheet->setCellValue('J' . $row, $data['data_vencimento_formatada'] ?: '');
        $sheet->setCellValue('K' . $row, $data['dias_para_vencimento'] ?: 0);
        $sheet->setCellValue('L' . $row, $data['valor_acordo'] ?: 0);
        $row++;
    }
    
    // Auto-ajustar colunas
    foreach (range('A', 'L') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    
    // Configurar download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="vencimentos_proximos_' . date('Y-m-d_H-i-s') . '.xlsx"');
    header('Cache-Control: max-age=0');
    
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
    
} elseif ($formato === 'pdf') {
    // Limpar qualquer output buffer antes de gerar o PDF
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);
    
    $dompdf = new Dompdf($options);
    
    $html = '
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; font-size: 10px; }
            .header { text-align: center; margin-bottom: 20px; }
            .summary { display: flex; justify-content: space-between; margin-bottom: 20px; }
            .summary-item { text-align: center; padding: 8px; background: #f8f9fa; border-radius: 5px; width: 23%; }
            table { width: 100%; border-collapse: collapse; }
            th, td { border: 1px solid #ddd; padding: 4px; text-align: left; font-size: 9px; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .text-right { text-align: right; }
            .text-center { text-align: center; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>RELATÓRIO DE VENCIMENTOS PRÓXIMOS</h1>
            <p>Gerado em: ' . date('d/m/Y H:i:s') . '</p>
            <p>Período: Próximos ' . $dias_vencimento . ' dias</p>
        </div>
        
        <div class="summary">
            <div class="summary-item">
                <strong>Vencidos</strong><br>
                ' . number_format($resumo_urgencia['VENCIDO']['count']) . '<br>
                <small>' . formatarValor($resumo_urgencia['VENCIDO']['valor']) . '</small>
            </div>
            <div class="summary-item">
                <strong>7 Dias</strong><br>
                ' . number_format($resumo_urgencia['VENCE_SEMANA']['count']) . '<br>
                <small>' . formatarValor($resumo_urgencia['VENCE_SEMANA']['valor']) . '</small>
            </div>
            <div class="summary-item">
                <strong>15 Dias</strong><br>
                ' . number_format($resumo_urgencia['VENCE_QUINZENA']['count']) . '<br>
                <small>' . formatarValor($resumo_urgencia['VENCE_QUINZENA']['valor']) . '</small>
            </div>
            <div class="summary-item">
                <strong>30 Dias</strong><br>
                ' . number_format($resumo_urgencia['VENCE_MES']['count']) . '<br>
                <small>' . formatarValor($resumo_urgencia['VENCE_MES']['valor']) . '</small>
            </div>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Associado</th>
                    <th>PA</th>
                    <th class="text-center">Parc.</th>
                    <th class="text-right">Valor</th>
                    <th>Vencimento</th>
                    <th class="text-center">Dias</th>
                </tr>
            </thead>
            <tbody>';
    
    foreach ($dados as $row) {
        $dias_texto = $row['dias_para_vencimento'] < 0 ? 
            abs($row['dias_para_vencimento']) . ' atrás' : 
            ($row['dias_para_vencimento'] == 0 ? 'Hoje' : $row['dias_para_vencimento'] . ' dias');
            
        $html .= '<tr>
            <td>' . htmlspecialchars(substr($row['associado_nome'] ?: '-', 0, 25)) . '</td>
            <td>' . htmlspecialchars($row['pa_nome'] ?: '-') . '</td>
            <td class="text-center">' . ($row['numero_parcela'] ?: '-') . '</td>
            <td class="text-right">' . formatarValor($row['valor_parcela']) . '</td>
            <td>' . ($row['data_vencimento_formatada'] ?: '-') . '</td>
            <td class="text-center">' . $dias_texto . '</td>
        </tr>';
    }
    
    $html .= '</tbody></table></body></html>';
    
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'landscape');
    $dompdf->render();
    
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="vencimentos_proximos_' . date('Y-m-d_H-i-s') . '.pdf"');
    
    echo $dompdf->output();
    exit;
}
?>