<?php
// Relatório Sintético de Acordos

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;
use Dompdf\Options;

// Construir WHERE clause baseado nos filtros
$where_conditions = [];
$params = [];

if (!empty($filtros['data_inicio'])) {
    $where_conditions[] = "a.data_acordo >= ?";
    $params[] = $filtros['data_inicio'];
}

if (!empty($filtros['data_fim'])) {
    $where_conditions[] = "a.data_acordo <= ?";
    $params[] = $filtros['data_fim'];
}

if (!empty($filtros['pa_id'])) {
    $where_conditions[] = "p.pa_id = ?";
    $params[] = $filtros['pa_id'];
}

if (!empty($filtros['advogado_id'])) {
    $where_conditions[] = "p.advogado_id = ?";
    $params[] = $filtros['advogado_id'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Query para resumo geral de acordos
$query_geral = "
    SELECT 
        COUNT(a.id) as total_acordos,
        COALESCE(SUM(a.valor_acordo), 0) as valor_total_acordos,
        COUNT(CASE WHEN a.ativo = 1 THEN 1 END) as acordos_ativos,
        COUNT(CASE WHEN a.ativo = 0 THEN 1 END) as acordos_inativos
    FROM cbp_acordos a
    LEFT JOIN cbp_processos_judiciais p ON a.processo_id = p.id
    $where_clause
";

$stmt = $pdo->prepare($query_geral);
$stmt->execute($params);
$dados_geral = $stmt->fetch(PDO::FETCH_ASSOC);

// Query para resumo de parcelas
$query_parcelas = "
    SELECT 
        COUNT(pa.id) as total_parcelas,
        COALESCE(SUM(pa.valor_parcela), 0) as valor_total_parcelas,
        COUNT(CASE WHEN pa.status = 'PAGO' THEN 1 END) as parcelas_pagas,
        COUNT(CASE WHEN pa.status = 'PENDENTE' THEN 1 END) as parcelas_pendentes,
        COUNT(CASE WHEN pa.status = 'VENCIDO' THEN 1 END) as parcelas_vencidas,
        COALESCE(SUM(CASE WHEN pa.status = 'PAGO' THEN pa.valor_parcela ELSE 0 END), 0) as valor_pago,
        COALESCE(SUM(CASE WHEN pa.status = 'PENDENTE' THEN pa.valor_parcela ELSE 0 END), 0) as valor_pendente
    FROM cbp_parcelas_acordo pa
    LEFT JOIN cbp_acordos a ON pa.acordo_id = a.id
    LEFT JOIN cbp_processos_judiciais p ON a.processo_id = p.id
    $where_clause
";

$stmt = $pdo->prepare($query_parcelas);
$stmt->execute($params);
$dados_parcelas = $stmt->fetch(PDO::FETCH_ASSOC);

// Query para acordos por PA
$query_pa = "
    SELECT 
        pa_tbl.nome as pa,
        COUNT(a.id) as quantidade,
        COALESCE(SUM(a.valor_acordo), 0) as valor_total
    FROM cbp_acordos a
    LEFT JOIN cbp_processos_judiciais p ON a.processo_id = p.id
    LEFT JOIN pontos_atendimento pa_tbl ON p.pa_id = pa_tbl.id
    $where_clause
    GROUP BY pa_tbl.id, pa_tbl.nome
    ORDER BY quantidade DESC
";

$stmt = $pdo->prepare($query_pa);
$stmt->execute($params);
$dados_pa = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Query para acordos por mês
$query_mes = "
    SELECT 
        DATE_FORMAT(a.data_acordo, '%Y-%m') as mes,
        DATE_FORMAT(a.data_acordo, '%m/%Y') as mes_formatado,
        COUNT(a.id) as quantidade,
        COALESCE(SUM(a.valor_acordo), 0) as valor_total
    FROM cbp_acordos a
    LEFT JOIN cbp_processos_judiciais p ON a.processo_id = p.id
    $where_clause
    GROUP BY DATE_FORMAT(a.data_acordo, '%Y-%m')
    ORDER BY mes DESC
    LIMIT 12
";

$stmt = $pdo->prepare($query_mes);
$stmt->execute($params);
$dados_mes = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Função para formatar valor
function formatarValor($valor) {
    return 'R$ ' . number_format($valor, 2, ',', '.');
}

// Gerar saída baseado no formato
if ($formato === 'html') {
    ?>
    <style>
        :root {
            --sicoob-turquesa: #00A091;
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-verde-claro: #C9D200;
            --sicoob-roxo: #49479D;
        }

        .contador-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            padding: 24px;
            text-align: center;
            border-left: 4px solid var(--sicoob-turquesa);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .contador-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .contador-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .contador-numero {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--sicoob-verde-escuro);
            margin-bottom: 8px;
        }

        .contador-label {
            color: #6c757d;
            font-size: 0.95rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .text-sicoob-primary { color: var(--sicoob-turquesa) !important; }
        .text-sicoob-success { color: var(--sicoob-verde-medio) !important; }
        .text-sicoob-purple { color: var(--sicoob-roxo) !important; }
        .text-sicoob-secondary { color: var(--sicoob-verde-escuro) !important; }

        .section-title {
            color: var(--sicoob-verde-escuro);
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--sicoob-turquesa);
        }

        .table-sicoob {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .table-sicoob thead th {
            background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-escuro) 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 12px 15px;
        }

        .table-sicoob tbody tr:hover {
            background-color: rgba(0, 160, 145, 0.05);
        }

        .table-sicoob tbody td {
            padding: 12px 15px;
            border-color: rgba(0, 160, 145, 0.1);
        }
    </style>

    <div class="relatorio-content">
        <!-- Cards de Resumo no estilo Contador com Ícone -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-handshake contador-icon text-sicoob-primary"></i>
                    <div class="contador-numero"><?= number_format($dados_geral['total_acordos']) ?></div>
                    <div class="contador-label">Total de Acordos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-dollar-sign contador-icon text-sicoob-success"></i>
                    <div class="contador-numero"><?= formatarValor($dados_geral['valor_total_acordos']) ?></div>
                    <div class="contador-label">Valor Total</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-check-circle contador-icon text-sicoob-primary"></i>
                    <div class="contador-numero"><?= number_format($dados_geral['acordos_ativos']) ?></div>
                    <div class="contador-label">Acordos Ativos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="contador-card">
                    <i class="fas fa-pause-circle contador-icon text-sicoob-purple"></i>
                    <div class="contador-numero"><?= number_format($dados_geral['acordos_inativos']) ?></div>
                    <div class="contador-label">Acordos Inativos</div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <h5 class="section-title">
                    <i class="fas fa-calendar-check me-2"></i>Resumo de Parcelas
                </h5>
                <div class="table-responsive">
                    <table class="table table-sicoob">
                        <thead>
                            <tr>
                                <th><i class="fas fa-list me-2"></i>Total de Parcelas</th>
                                <th class="text-end"><i class="fas fa-check me-2"></i>Parcelas Pagas</th>
                                <th class="text-end"><i class="fas fa-clock me-2"></i>Parcelas Pendentes</th>
                                <th class="text-end"><i class="fas fa-exclamation-triangle me-2"></i>Parcelas Vencidas</th>
                                <th class="text-end"><i class="fas fa-dollar-sign me-2"></i>Valor Pago</th>
                                <th class="text-end"><i class="fas fa-hourglass-half me-2"></i>Valor Pendente</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><?= number_format($dados_parcelas['total_parcelas']) ?></td>
                                <td class="text-end text-success"><?= number_format($dados_parcelas['parcelas_pagas']) ?></td>
                                <td class="text-end text-warning"><?= number_format($dados_parcelas['parcelas_pendentes']) ?></td>
                                <td class="text-end text-danger"><?= number_format($dados_parcelas['parcelas_vencidas']) ?></td>
                                <td class="text-end text-success"><?= formatarValor($dados_parcelas['valor_pago']) ?></td>
                                <td class="text-end text-warning"><?= formatarValor($dados_parcelas['valor_pendente']) ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h5>Acordos por Ponto de Atendimento</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>PA</th>
                                <th class="text-end">Quantidade</th>
                                <th class="text-end">Valor Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($dados_pa as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['pa'] ?: 'Não informado') ?></td>
                                <td class="text-end"><?= number_format($row['quantidade']) ?></td>
                                <td class="text-end"><?= formatarValor($row['valor_total']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-md-6">
                <h5>Acordos por Mês (Últimos 12 meses)</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Mês/Ano</th>
                                <th class="text-end">Quantidade</th>
                                <th class="text-end">Valor Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($dados_mes as $row): ?>
                            <tr>
                                <td><?= htmlspecialchars($row['mes_formatado']) ?></td>
                                <td class="text-end"><?= number_format($row['quantidade']) ?></td>
                                <td class="text-end"><?= formatarValor($row['valor_total']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php
} elseif ($formato === 'excel') {
    // Limpar qualquer output buffer antes de gerar o Excel
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Acordos Sintético');
    
    // Cabeçalho
    $sheet->setCellValue('A1', 'RELATÓRIO SINTÉTICO DE ACORDOS');
    $sheet->mergeCells('A1:D1');
    $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
    
    // Resumo geral
    $row = 3;
    $sheet->setCellValue('A' . $row, 'Total de Acordos:');
    $sheet->setCellValue('B' . $row, $dados_geral['total_acordos']);
    $row++;
    $sheet->setCellValue('A' . $row, 'Valor Total dos Acordos:');
    $sheet->setCellValue('B' . $row, $dados_geral['valor_total_acordos']);
    $row++;
    $sheet->setCellValue('A' . $row, 'Acordos Ativos:');
    $sheet->setCellValue('B' . $row, $dados_geral['acordos_ativos']);
    $row++;
    $sheet->setCellValue('A' . $row, 'Acordos Quitados:');
    $sheet->setCellValue('B' . $row, $dados_geral['acordos_quitados']);
    
    // Configurar download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="acordos_sintetico_' . date('Y-m-d_H-i-s') . '.xlsx"');
    header('Cache-Control: max-age=0');
    
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
    
} elseif ($formato === 'pdf') {
    // Limpar qualquer output buffer antes de gerar o PDF
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    $options = new Options();
    $options->set('defaultFont', 'Arial');
    $options->set('isRemoteEnabled', true);
    
    $dompdf = new Dompdf($options);
    
    $html = '
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; font-size: 12px; }
            .header { text-align: center; margin-bottom: 20px; }
            .totals { background: #f8f9fa; padding: 10px; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .text-right { text-align: right; }
            .section-title { font-weight: bold; margin-top: 20px; margin-bottom: 10px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>RELATÓRIO SINTÉTICO DE ACORDOS</h1>
            <p>Gerado em: ' . date('d/m/Y H:i:s') . '</p>
        </div>
        
        <div class="totals">
            <p><strong>Total de Acordos:</strong> ' . number_format($dados_geral['total_acordos']) . '</p>
            <p><strong>Valor Total dos Acordos:</strong> ' . formatarValor($dados_geral['valor_total_acordos']) . '</p>
            <p><strong>Acordos Ativos:</strong> ' . number_format($dados_geral['acordos_ativos']) . '</p>
            <p><strong>Acordos Quitados:</strong> ' . number_format($dados_geral['acordos_quitados']) . '</p>
        </div>
    </body>
    </html>';
    
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();
    
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="acordos_sintetico_' . date('Y-m-d_H-i-s') . '.pdf"');
    
    echo $dompdf->output();
    exit;
}
?>